<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="app" target="17" />
      <module name="modules" target="17" />
      <module name="plugins" target="17" />
      <module name="yy-zs-agent-app" target="17" />
      <module name="yy-zs-integration-plugin" target="17" />
      <module name="yy-zs-logging-plugin" target="17" />
      <module name="yy-zs-monitor-app" target="17" />
      <module name="yy-zs-security-plugin" target="17" />
      <module name="yyzs-admin" target="17" />
      <module name="yyzs-agent" target="17" />
      <module name="yyzs-api" target="17" />
      <module name="yyzs-cache" target="17" />
      <module name="yyzs-common" target="17" />
      <module name="yyzs-database" target="17" />
      <module name="yyzs-file" target="17" />
      <module name="yyzs-monitor" target="17" />
      <module name="yyzs-notification" target="17" />
      <module name="yyzs-system" target="17" />
      <module name="yyzs-workflow" target="17" />
    </bytecodeTargetLevel>
  </component>
</project>